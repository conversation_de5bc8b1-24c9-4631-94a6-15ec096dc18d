'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { menuItems } from '@/mock/menuItemsData';

interface MenuItemDetailPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    itemId: string;
  }>;
}

export default function MenuItemDetailPage({ params }: MenuItemDetailPageProps) {
  const { slugShop, slugBranch, itemId } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { shop } = branchWithShop;

  // Find the menu item
  const menuItem = menuItems.find(item => item.id === itemId);

  if (!menuItem) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Menu Item Not Found</h1>
          <p className="text-[#81766a] text-sm">The menu item you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Mock additional data for the detail view
  const mockTags = ['Gluten-Free', 'Dairy-Free', 'Nut-Free'];
  const mockIngredients = 'Romaine lettuce, grilled chicken breast, croutons, parmesan cheese, Caesar dressing.';
  const mockNutritionalInfo = 'Calories: 450, Fat: 25g, Protein: 30g, Carbs: 20g';
  const mockPreparationTime = '15 minutes';

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">
          {/* Main Content */}
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Breadcrumb */}
            <div className="flex flex-wrap gap-2 p-4">
              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`} className="text-[#81766a] text-base font-medium leading-normal">
                Menu
              </Link>
              <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
              <span className="text-[#161412] text-base font-medium leading-normal">{menuItem.name}</span>
            </div>

            {/* Header */}
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">{menuItem.name}</p>
                <p className="text-[#81766a] text-sm font-normal leading-normal">
                  {menuItem.description}
                </p>
              </div>
            </div>

            {/* Image */}
            <div className="flex w-full grow bg-white p-4">
              <div className="w-full gap-1 overflow-hidden bg-white aspect-[3/2] rounded-xl flex">
                <div
                  className="w-full bg-center bg-no-repeat bg-cover aspect-auto rounded-none flex-1"
                  style={{
                    backgroundImage: `url("${menuItem.image}")`,
                  }}
                  onError={(e) => {
                    e.currentTarget.style.backgroundImage = 'url("https://via.placeholder.com/600x400?text=No+Image")';
                  }}
                ></div>
              </div>
            </div>

            {/* Description */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Description</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">
              {menuItem.description}
            </p>

            {/* Tags */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Tags</h3>
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              {mockTags.map((tag, index) => (
                <div key={index} className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#f4f2f1] pl-4 pr-4">
                  <p className="text-[#161412] text-sm font-medium leading-normal">{tag}</p>
                </div>
              ))}
            </div>

            {/* Ingredients */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Ingredients</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{mockIngredients}</p>

            {/* Pricing */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Pricing</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">${menuItem.price.toFixed(2)}</p>

            {/* Nutritional Information */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Nutritional Information</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{mockNutritionalInfo}</p>

            {/* Preparation Time */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Preparation Time</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{mockPreparationTime}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

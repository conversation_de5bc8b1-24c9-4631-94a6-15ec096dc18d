'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Plus, Search, Edit, Trash2, Eye } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { menuItems } from '@/mock/menuItemsData';

interface MenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function MenuPage({ params }: MenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  // Filter menu items based on search term and category
  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = activeTab === 'all' || item.category === activeTab;
    return matchesSearch && matchesCategory;
  });

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(menuItems.map(item => item.category)))];

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Menu Management</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage menu items for {shop.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-end">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/add`}>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Menu Item
            </Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="bg-[#f1edea] p-1">
            {categories.map((category) => (
              <TabsTrigger
                key={category}
                value={category}
                className="data-[state=active]:bg-white capitalize"
              >
                {category === 'all' ? 'All Items' : category}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="flex-1 max-w-sm ml-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <Input
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        <TabsContent value={activeTab} className="mt-0">
          {filteredItems.length === 0 ? (
            <div className="flex items-center justify-center h-64 text-[#8a745c]">
              {searchTerm ? 'No menu items match your search' : 'No menu items found'}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredItems.map((item) => (
                <Card key={item.id} className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="aspect-video bg-[#f1edea] rounded-md mb-3 flex items-center justify-center">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover rounded-md"
                        onError={(e) => {
                          e.currentTarget.src = 'https://via.placeholder.com/300x200?text=No+Image';
                        }}
                      />
                    </div>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-[#181510] text-lg">{item.name}</CardTitle>
                      <Badge
                        className={`${
                          item.available
                            ? 'bg-green-100 text-green-800 hover:bg-green-100'
                            : 'bg-red-100 text-red-800 hover:bg-red-100'
                        }`}
                      >
                        {item.available ? 'Available' : 'Unavailable'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-[#8a745c] text-sm mb-3 line-clamp-2">{item.description}</p>
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-[#181510] text-xl font-bold">${item.price.toFixed(2)}</span>
                      <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4]">
                        {item.category}
                      </Badge>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="flex-1 border-[#e2dcd4]">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1 border-[#e2dcd4]">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm" className="border-red-200 text-red-600 hover:bg-red-50">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Search, Plus } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { menuItems } from '@/mock/menuItemsData';

interface MenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function MenuPage({ params }: MenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#181511] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#887663] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { shop } = branchWithShop;

  // Filter menu items based on search term
  const filteredItems = menuItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">

          {/* Main Content */}
          <div className="layout-content-container flex flex-col flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight">Menu Management</p>
                <p className="text-[#887663] text-sm font-normal leading-normal">
                  Manage your restaurant's menu items, including descriptions, ingredients, pricing, and availability.
                </p>
              </div>
              <div className="flex items-end">
                <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/add`}>
                  <Button className="bg-[#887663] hover:bg-[#6d5a48] text-white flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Menu Item
                  </Button>
                </Link>
              </div>
            </div>

            {/* Search */}
            <div className="px-4 py-3">
              <label className="flex flex-col min-w-40 h-12 w-full">
                <div className="flex w-full flex-1 items-stretch rounded-xl h-full">
                  <div className="text-[#887663] flex border-none bg-[#f4f2f0] items-center justify-center pl-4 rounded-l-xl border-r-0">
                    <Search size={24} />
                  </div>
                  <input
                    placeholder="Search menu items"
                    className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-full placeholder:text-[#887663] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </label>
            </div>

            {/* Menu Items Table */}
            <h2 className="text-[#181511] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Menu Items</h2>
            <div className="px-4 py-3">
              <div className="flex overflow-hidden rounded-xl border border-[#e5e1dc] bg-white">
                <table className="flex-1">
                  <thead>
                    <tr className="bg-white">
                      <th className="px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                        Item Name
                      </th>
                      <th className="px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                        Category
                      </th>
                      <th className="px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Price</th>
                      <th className="px-4 py-3 text-left text-[#181511] w-60 text-sm font-medium leading-normal">Availability</th>
                      <th className="px-4 py-3 text-left text-[#887663] w-60 text-sm font-medium leading-normal">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredItems.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="h-[72px] px-4 py-2 text-center text-[#887663] text-sm">
                          {searchTerm ? 'No menu items match your search' : 'No menu items found'}
                        </td>
                      </tr>
                    ) : (
                      filteredItems.map((item) => (
                        <tr key={item.id} className="border-t border-t-[#e5e1dc]">
                          <td className="h-[72px] px-4 py-2 w-[400px] text-[#181511] text-sm font-normal leading-normal">
                            {item.name}
                          </td>
                          <td className="h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                            {item.category}
                          </td>
                          <td className="h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                            ${item.price.toFixed(2)}
                          </td>
                          <td className="h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                            <button
                              className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                                item.available
                                  ? 'bg-[#f4f2f0] text-[#181511]'
                                  : 'bg-red-100 text-red-800'
                              }`}
                            >
                              <span className="truncate">{item.available ? 'Available' : 'Unavailable'}</span>
                            </button>
                          </td>
                          <td className="h-[72px] px-4 py-2 w-60 text-[#887663] text-sm font-bold leading-normal tracking-[0.015em]">
                            <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.id}`} className="hover:text-[#181511] transition-colors">
                              View Details
                            </Link>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

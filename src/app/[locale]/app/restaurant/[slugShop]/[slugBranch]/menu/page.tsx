'use client';

import { useEffect } from 'react';
import { useRouter } from '@/i18n/navigation';
import { AppLoading } from '@/components/ui/app-loading';
import { mockShopsWithBranches } from '@/mock/shopData';

export default function MenuPage() {
  const router = useRouter();

  // Get the first shop and branch from mock data for redirection
  const firstShop = mockShopsWithBranches[0];
  const firstBranch = firstShop?.branches[0];

  useEffect(() => {
    // Redirect to the new URL structure
    if (firstShop && firstBranch) {
      router.push(`/app/restaurant/${firstShop.slug}/${firstBranch.slug}/menu`);
    } else {
      // Fallback to the restaurant page if no shop/branch is found
      router.push('/app/restaurant');
    }
  }, [router, firstShop, firstBranch]);

  // Show loading while redirecting
  return <AppLoading type="restaurant" size="lg" />;
}

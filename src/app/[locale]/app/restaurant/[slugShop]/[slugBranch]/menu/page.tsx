'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Search } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { menuItems } from '@/mock/menuItemsData';
import { FallbackBackgroundImage } from '@/components/ui/fallback-image';

interface MenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function MenuPage({ params }: MenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('grid');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { shop } = branchWithShop;

  // Filter menu items based on search term
  const filteredItems = menuItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">

          {/* Main Content */}
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Menu Management</p>
                <p className="text-[#81766a] text-sm font-normal leading-normal">
                  Manage your restaurant's menu items, including descriptions, ingredients, pricing, and availability.
                </p>
              </div>
            </div>

            {/* Search */}
            <div className="px-4 py-3">
              <label className="flex flex-col min-w-40 h-12 w-full">
                <div className="flex w-full flex-1 items-stretch rounded-xl h-full">
                  <div className="text-[#81766a] flex border-none bg-[#f4f2f1] items-center justify-center pl-4 rounded-l-xl border-r-0">
                    <Search size={24} />
                  </div>
                  <input
                    placeholder="Search menu items"
                    className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-full placeholder:text-[#81766a] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </label>
            </div>

            {/* Menu Items Header */}
            <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Menu Items</h2>

            {/* View Toggle */}
            <div className="pb-3">
              <div className="flex border-b border-[#e3e1dd] px-4 gap-8">
                <button
                  onClick={() => setViewMode('table')}
                  className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                    viewMode === 'table'
                      ? 'border-b-[#161412] text-[#161412]'
                      : 'border-b-transparent text-[#81766a]'
                  }`}
                >
                  <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                    viewMode === 'table' ? 'text-[#161412]' : 'text-[#81766a]'
                  }`}>Table View</p>
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                    viewMode === 'grid'
                      ? 'border-b-[#161412] text-[#161412]'
                      : 'border-b-transparent text-[#81766a]'
                  }`}
                >
                  <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                    viewMode === 'grid' ? 'text-[#161412]' : 'text-[#81766a]'
                  }`}>Grid View</p>
                </button>
              </div>
            </div>

            {/* Content based on view mode */}
            {viewMode === 'table' ? (
              /* Table View */
              <div className="px-4 py-3">
                <div className="flex overflow-hidden rounded-xl border border-[#e3e1dd] bg-white">
                  <table className="flex-1">
                    <thead>
                      <tr className="bg-white">
                        <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                          Item Name
                        </th>
                        <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                          Category
                        </th>
                        <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">Price</th>
                        <th className="px-4 py-3 text-left text-[#161412] w-60 text-sm font-medium leading-normal">Availability</th>
                        <th className="px-4 py-3 text-left text-[#81766a] w-60 text-sm font-medium leading-normal">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredItems.length === 0 ? (
                        <tr>
                          <td colSpan={5} className="h-[72px] px-4 py-2 text-center text-[#81766a] text-sm">
                            {searchTerm ? 'No menu items match your search' : 'No menu items found'}
                          </td>
                        </tr>
                      ) : (
                        filteredItems.map((item) => (
                          <tr key={item.id} className="border-t border-t-[#e3e1dd]">
                            <td className="h-[72px] px-4 py-2 w-[400px] text-[#161412] text-sm font-normal leading-normal">
                              {item.name}
                            </td>
                            <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                              {item.category}
                            </td>
                            <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                              ${item.price.toFixed(2)}
                            </td>
                            <td className="h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                              <button
                                className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                                  item.available
                                    ? 'bg-[#f4f2f1] text-[#161412]'
                                    : 'bg-red-100 text-red-800'
                                }`}
                              >
                                <span className="truncate">{item.available ? 'Available' : 'Unavailable'}</span>
                              </button>
                            </td>
                            <td className="h-[72px] px-4 py-2 w-60 text-[#81766a] text-sm font-bold leading-normal tracking-[0.015em]">
                              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}`} className="hover:text-[#161412] transition-colors">
                                View Details
                              </Link>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              /* Grid View */
              <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
                {filteredItems.length === 0 ? (
                  <div className="col-span-full flex items-center justify-center h-64 text-[#81766a]">
                    {searchTerm ? 'No menu items match your search' : 'No menu items found'}
                  </div>
                ) : (
                  filteredItems.map((item) => (
                    <Link key={item.id} href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}`}>
                      <div className="flex flex-col gap-3 pb-3 cursor-pointer hover:opacity-80 transition-opacity">
                        <FallbackBackgroundImage
                          src={item.image}
                          className="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
                          fallbackSrc="https://via.placeholder.com/400x400/f4f2f1/161412?text=No+Image"
                        />
                        <div>
                          <p className="text-[#161412] text-base font-medium leading-normal">{item.name}</p>
                          <p className="text-[#81766a] text-sm font-normal leading-normal">{item.category} - ${item.price.toFixed(2)}</p>
                        </div>
                      </div>
                    </Link>
                  ))
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

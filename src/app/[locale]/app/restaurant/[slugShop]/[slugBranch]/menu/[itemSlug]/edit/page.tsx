'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { getMenuItemBySlug } from '@/mock/menuItemsData';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Save } from 'lucide-react';

interface MenuItemEditPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    itemSlug: string;
  }>;
}

export default function MenuItemEditPage({ params }: MenuItemEditPageProps) {
  const { slugShop, slugBranch, itemSlug } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { shop } = branchWithShop;

  // Find the menu item
  const menuItem = getMenuItemBySlug(itemSlug);

  if (!menuItem) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Menu Item Not Found</h1>
          <p className="text-[#81766a] text-sm">The menu item you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Form state
  const [formData, setFormData] = useState({
    name: menuItem.name,
    description: menuItem.description,
    price: menuItem.price.toString(),
    category: menuItem.category,
    image: menuItem.image,
    available: menuItem.available
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically save the data
    console.log('Saving menu item:', formData);
    // For now, just show an alert
    alert('Menu item updated successfully!');
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">
          {/* Sidebar */}
          <div className="layout-content-container flex flex-col w-80">
            <div className="flex h-full min-h-[700px] flex-col justify-between bg-white p-4">
              <div className="flex flex-col gap-4">
                <h1 className="text-[#161412] text-base font-medium leading-normal">{shop.name}</h1>
                <div className="flex flex-col gap-2">
                  <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
                    <div className="flex items-center gap-3 px-3 py-2">
                      <div className="text-[#161412]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                          <path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.10Z"></path>
                        </svg>
                      </div>
                      <p className="text-[#161412] text-sm font-medium leading-normal">Dashboard</p>
                    </div>
                  </Link>
                  <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders`}>
                    <div className="flex items-center gap-3 px-3 py-2">
                      <div className="text-[#161412]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                          <path d="M72,104a8,8,0,0,1,8-8h96a8,8,0,0,1,0,16H80A8,8,0,0,1,72,104Zm8,40h96a8,8,0,0,0,0-16H80a8,8,0,0,0,0,16ZM232,56V208a8,8,0,0,1-11.58,7.15L192,200.94l-28.42,14.21a8,8,0,0,1-7.16,0L128,200.94,99.58,215.15a8,8,0,0,1-7.16,0L64,200.94,35.58,215.15A8,8,0,0,1,24,208V56A16,16,0,0,1,40,40H216A16,16,0,0,1,232,56Zm-16,0H40V195.06l20.42-10.22a8,8,0,0,1,7.16,0L96,199.06l28.42-14.22a8,8,0,0,1,7.16,0L160,199.06l28.42-14.22a8,8,0,0,1,7.16,0L216,195.06Z"></path>
                        </svg>
                      </div>
                      <p className="text-[#161412] text-sm font-medium leading-normal">Orders</p>
                    </div>
                  </Link>
                  <div className="flex items-center gap-3 px-3 py-2 rounded-full bg-[#f4f2f1]">
                    <div className="text-[#161412]">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M239.54,63a15.91,15.91,0,0,0-7.25-9.9,201.49,201.49,0,0,0-208.58,0,16,16,0,0,0-5.37,22l96,157.27a16,16,0,0,0,27.36,0l96-157.27A15.82,15.82,0,0,0,239.54,63Zm-55.1,68.53a40,40,0,0,0-41.38,67.77L128,224,96.5,172.43a40,40,0,1,0-41.35-67.76L48.8,94.26a152,152,0,0,1,158.39,0Z"></path>
                      </svg>
                    </div>
                    <p className="text-[#161412] text-sm font-medium leading-normal">Menu</p>
                  </div>
                  <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
                    <div className="flex items-center gap-3 px-3 py-2">
                      <div className="text-[#161412]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                          <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z"></path>
                        </svg>
                      </div>
                      <p className="text-[#161412] text-sm font-medium leading-normal">Staff</p>
                    </div>
                  </Link>
                  <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
                    <div className="flex items-center gap-3 px-3 py-2">
                      <div className="text-[#161412]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                          <path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"></path>
                        </svg>
                      </div>
                      <p className="text-[#161412] text-sm font-medium leading-normal">Settings</p>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Back Button */}
            <div className="flex items-center mb-6 p-4">
              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${itemSlug}`}>
                <Button variant="outline" className="border-[#e2dcd4] text-[#161412]">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Menu Item
                </Button>
              </Link>
            </div>

            {/* Header */}
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Edit Menu Item</p>
                <p className="text-[#81766a] text-sm font-normal leading-normal">
                  Update the details for "{menuItem.name}"
                </p>
              </div>
            </div>

            {/* Edit Form */}
            <div className="p-4">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Item Name */}
                  <div className="space-y-2">
                    <label className="text-[#161412] text-sm font-medium leading-normal">Item Name</label>
                    <Input
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="border-[#e5e1dc]"
                      placeholder="Enter item name"
                    />
                  </div>

                  {/* Price */}
                  <div className="space-y-2">
                    <label className="text-[#161412] text-sm font-medium leading-normal">Price ($)</label>
                    <Input
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', e.target.value)}
                      className="border-[#e5e1dc]"
                      placeholder="0.00"
                    />
                  </div>

                  {/* Category */}
                  <div className="space-y-2">
                    <label className="text-[#161412] text-sm font-medium leading-normal">Category</label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger className="border-[#e5e1dc]">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Appetizers">Appetizers</SelectItem>
                        <SelectItem value="Main Courses">Main Courses</SelectItem>
                        <SelectItem value="Desserts">Desserts</SelectItem>
                        <SelectItem value="Drinks">Drinks</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Availability */}
                  <div className="space-y-2">
                    <label className="text-[#161412] text-sm font-medium leading-normal">Availability</label>
                    <Select 
                      value={formData.available ? 'true' : 'false'} 
                      onValueChange={(value) => handleInputChange('available', value === 'true')}
                    >
                      <SelectTrigger className="border-[#e5e1dc]">
                        <SelectValue placeholder="Select availability" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Available</SelectItem>
                        <SelectItem value="false">Unavailable</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <label className="text-[#161412] text-sm font-medium leading-normal">Description</label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="border-[#e5e1dc] min-h-[100px]"
                    placeholder="Enter item description"
                  />
                </div>

                {/* Image URL */}
                <div className="space-y-2">
                  <label className="text-[#161412] text-sm font-medium leading-normal">Image URL</label>
                  <Input
                    value={formData.image}
                    onChange={(e) => handleInputChange('image', e.target.value)}
                    className="border-[#e5e1dc]"
                    placeholder="Enter image URL"
                  />
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-4">
                  <Button type="submit" className="bg-[#887663] hover:bg-[#6d5a48] text-white flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
